using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Boards;
using DeviceGuardCloud.Application.Boards.Commands;
using DeviceGuardCloud.Application.Security.Queries;
using DeviceGuardCloud.Application.Security.Queries.Models;
using DeviceGuardCloud.DomainModel.Boards;
using Doulex.AspNetCore.Authorization;
using Doulex.DomainDriven;
using Moq;

namespace DeviceGuardCloud.Application.Logic.Tests;

public class UpdateBoardCommandHandlerTests
{
    private readonly Mock<IBoardRepository> _mockBoardRepository;
    private readonly Mock<IBoardValidator>  _mockBoardValidator;
    private readonly Mock<IUnitOfWork>      _mockUnitOfWork;
    private readonly Mock<IAuditLogService> _mockAuditLogService;
    private readonly Mock<ILoginUserQuery>  _mockLoginUserQuery;
    private readonly Mock<ITransaction>     _mockTransaction;
    private readonly UpdateBoardCommandHandler _handler;

    public UpdateBoardCommandHandlerTests()
    {
        _mockBoardRepository = new Mock<IBoardRepository>();
        _mockBoardValidator  = new Mock<IBoardValidator>();
        _mockUnitOfWork      = new Mock<IUnitOfWork>();
        _mockAuditLogService = new Mock<IAuditLogService>();
        _mockLoginUserQuery  = new Mock<ILoginUserQuery>();
        _mockTransaction     = new Mock<ITransaction>();

        _handler = new UpdateBoardCommandHandler(
            _mockBoardRepository.Object,
            _mockBoardValidator.Object,
            _mockUnitOfWork.Object,
            _mockAuditLogService.Object,
            _mockLoginUserQuery.Object);
    }

    [Fact]
    public async Task Handle_ShouldUpdateBoard_WhenCommandIsValid()
    {
        // Arrange
        var boardId = Guid.NewGuid();
        var command = new UpdateBoardCommand
        {
            Id = boardId,
            AgencyName = "Updated Agency",
            OwnedBy = Guid.NewGuid()
        };

        var loginUser = new LoginUserModel
        {
            UserId = Guid.NewGuid(),
            UserName = "testuser"
        };

        var existingBoard = new Board
        {
            Model = "TestModel",
            SerialNumber = "SN123456",
            AesKey = "TestAesKey",
            AgencyName = "Original Agency",
            OwnedBy = null,
            CreatedBy = Guid.NewGuid()
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                          .ReturnsAsync(loginUser);
        _mockBoardRepository.Setup(x => x.GetAsync(boardId, It.IsAny<CancellationToken>()))
                           .ReturnsAsync(existingBoard);
        _mockUnitOfWork.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
                      .ReturnsAsync(_mockTransaction.Object);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.Equal("Updated Agency", existingBoard.AgencyName);
        Assert.Equal(command.OwnedBy, existingBoard.OwnedBy);
        Assert.Equal(loginUser.UserId, existingBoard.UpdatedBy);
        Assert.NotNull(existingBoard.UpdatedAt);

        _mockBoardValidator.Verify(x => x.ValidateAsync(existingBoard, It.IsAny<CancellationToken>()), Times.Once);
        _mockBoardRepository.Verify(x => x.UpdateAsync(existingBoard, It.IsAny<CancellationToken>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockAuditLogService.Verify(x => x.LogAsync(It.IsAny<AuditLog>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockTransaction.Verify(x => x.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldSetFieldsToNull_WhenCommandHasNullValues()
    {
        // Arrange
        var boardId = Guid.NewGuid();
        var command = new UpdateBoardCommand
        {
            Id = boardId,
            AgencyName = null,
            OwnedBy = null
        };

        var loginUser = new LoginUserModel
        {
            UserId = Guid.NewGuid(),
            UserName = "testuser"
        };

        var existingBoard = new Board
        {
            Model = "TestModel",
            SerialNumber = "SN123456",
            AesKey = "TestAesKey",
            AgencyName = "Original Agency",
            OwnedBy = Guid.NewGuid(),
            CreatedBy = Guid.NewGuid()
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                          .ReturnsAsync(loginUser);
        _mockBoardRepository.Setup(x => x.GetAsync(boardId, It.IsAny<CancellationToken>()))
                           .ReturnsAsync(existingBoard);
        _mockUnitOfWork.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
                      .ReturnsAsync(_mockTransaction.Object);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.Null(existingBoard.AgencyName);
        Assert.Null(existingBoard.OwnedBy);

        _mockBoardValidator.Verify(x => x.ValidateAsync(existingBoard, It.IsAny<CancellationToken>()), Times.Once);
        _mockBoardRepository.Verify(x => x.UpdateAsync(existingBoard, It.IsAny<CancellationToken>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockAuditLogService.Verify(x => x.LogAsync(It.IsAny<AuditLog>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockTransaction.Verify(x => x.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowAuthorizeException_WhenUserNotLoggedIn()
    {
        // Arrange
        var command = new UpdateBoardCommand
        {
            Id = Guid.NewGuid(),
            AgencyName = "Test Agency"
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                          .ReturnsAsync((LoginUserModel?)null);

        // Act & Assert
        await Assert.ThrowsAsync<AuthorizeException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldThrowArgumentException_WhenBoardNotFound()
    {
        // Arrange
        var command = new UpdateBoardCommand
        {
            Id = Guid.NewGuid(),
            AgencyName = "Test Agency"
        };

        var loginUser = new LoginUserModel
        {
            UserId = Guid.NewGuid(),
            UserName = "testuser"
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                          .ReturnsAsync(loginUser);
        _mockBoardRepository.Setup(x => x.GetAsync(command.Id, It.IsAny<CancellationToken>()))
                           .ReturnsAsync((Board?)null);

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(command, CancellationToken.None));
    }
}
