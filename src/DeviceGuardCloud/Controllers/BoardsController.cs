using AspNetCore.Abstractions.Exceptions;
using DeviceGuardCloud.Application.Boards.Commands;
using DeviceGuardCloud.Application.Boards.Queries;
using DeviceGuardCloud.WebApi.Authorization;
using DeviceGuardCloud.WebApi.Models;
using LinqAsync.Pages;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace DeviceGuardCloud.WebApi.Controllers
{
    // 出厂添加 Board, 然后后期能激活
    /// <summary>
    /// 激活检测仪设备API
    /// </summary>
    [Route("v1/[controller]")]
    public class BoardsController : Controller
    {
        private readonly IMediator   _mediator;
        private readonly IBoardQuery _boardQuery;

        public BoardsController(IMediator mediator, IBoardQuery boardQuery)
        {
            _mediator   = mediator;
            _boardQuery = boardQuery;
        }

        /// <summary>
        /// 获取许可证列表
        /// </summary>
        ///  <param name="query">查询条件</param>
        ///  <param name="cancel">取消操作的Token</param>
        /// <returns></returns>
        [HttpGet()]
        [PermissionAuthorize(AdminPermission.BoardRead)]
        public async Task<Page<BoardViewModel>> GetBoardsAsync([FromQuery] BoardQueryPredicate query, CancellationToken cancel)
        {
            var result = await _boardQuery.GetBoardsAsync(query, cancel);
            return result;
        }

        /// <summary>
        /// 根据ID获取许可证
        /// </summary>
        /// <param name="boardId">要查询的Board的对象Id</param>
        /// <param name="cancel">取消操作的Token</param>
        /// <returns></returns>
        [HttpGet("{boardId}")]
        [PermissionAuthorize(AdminPermission.BoardRead)]
        public async Task<BoardViewModel> GeBoardAsync(Guid boardId, CancellationToken cancel)
        {
            var board = await _boardQuery.GetBoardAsync(boardId, cancel) ?? throw new NotFoundException("许可证不存在");
            return board;
        }

        /// <summary>
        /// 创建 Board
        /// </summary>
        /// <param name="command">创建 Board 的命令</param>
        /// <returns></returns>
        [HttpPost]
        [PermissionAuthorize(AdminPermission.BoardCreate)]
        public async Task CreateBoardAsync([FromBody] CreateBoardCommand command)
        {
            await _mediator.Send(command);
        }

        /// <summary>
        /// 激活 Board
        /// </summary>
        /// <param name="model">检测仪设备的型号</param>
        /// <param name="sn">检测仪设备的序列号</param>
        /// <returns></returns>
        [HttpPost("/api/v1/board-license/activate")]
        [Authorize()]
        [ProducesResponseType<ActivateResponseModel>(200)]
        public async Task<ActivateResponseModel> ActivateBoardAsync([FromQuery] string model, [FromQuery] string sn)
        {
            var boardCommand = new ActivateBoardCommand
            {
                Model        = model,
                SerialNumber = sn
            };
            var productKey = await _mediator.Send(boardCommand);

            return new ActivateResponseModel
            {
                ProductKey = productKey
            };
        }

        /// <summary>
        /// 删除Board
        /// </summary>
        /// <param name="boardId">要删除的Board的Id</param>
        /// <param name="cancel">取消操作的Token</param>
        /// <returns></returns>
        [HttpDelete("{boardId}")]
        [PermissionAuthorize(AdminPermission.BoardDelete)]
        public async Task DeleteBoardAsync(Guid boardId, CancellationToken cancel)
        {
            var command = new RemoveBoardCommand() { Id = boardId };
            await _mediator.Send(command, cancel);
        }
    }
}
